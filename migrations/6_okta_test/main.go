package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

const (
	TENANT_ID = "1SO49IoBbC0Roq8vMMWC"
)

type UserSearchResponse struct {
	Hits struct {
		Hits []struct {
			Source common.IDPUsersDoc `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

type EventAggregationResponse struct {
	Aggregations struct {
		ActorIds struct {
			Buckets []struct {
				Key      string `json:"key"`
				DocCount int    `json:"doc_count"`
			} `json:"buckets"`
		} `json:"actor_ids"`
	} `json:"aggregations"`
}

// getUserIdByEmail searches for a user by email and returns their userId
func getUserIdByEmail(email string) (string, error) {
	// Create terms query to search for user by email
	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"terms": {
							"email.keyword": ["%s"]
						}
					}
				]
			}
		},
		"size": 1
	}`, TENANT_ID, email)

	// Execute the search query
	docs, err := elastic.ExecuteSearchQuery([]string{elastic.IDP_USERS_INDEX}, query)
	if err != nil {
		return "", fmt.Errorf("failed to execute search query: %w", err)
	}

	if len(docs) == 0 {
		return "", nil // No user found
	}

	// Parse the response to get userId
	docBytes, err := json.Marshal(docs[0])
	if err != nil {
		return "", fmt.Errorf("failed to marshal document: %w", err)
	}

	var user common.IDPUsersDoc
	if err := json.Unmarshal(docBytes, &user); err != nil {
		return "", fmt.Errorf("failed to unmarshal user document: %w", err)
	}

	return user.UserID, nil
}

// getActorIdsForTargetUser searches idp_events for activities on the target user and returns unique actorIds
func getActorIdsForTargetUser(targetUserId string) ([]string, error) {
	// Create aggregation query to get unique actorIds for events targeting this user
	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"nested": {
							"path": "targets",
							"query": {
								"bool": {
									"must": [
										{
											"match": {
												"targets.targetId": "%s"
											}
										}
									]
								}
							}
						}
					}
				]
			}
		},
		"size": 0,
		"aggs": {
			"actor_ids": {
				"terms": {
					"field": "actorId.keyword",
					"size": 100
				}
			}
		}
	}`, TENANT_ID, targetUserId)

	// Execute the aggregation query
	aggResponse, err := elastic.ExecuteSearchForAggregation([]string{elastic.IDP_EVENTS_INDEX}, query)
	if err != nil {
		return nil, fmt.Errorf("failed to execute aggregation query: %w", err)
	}

	var actorIds []string
	if actorIdsAgg, ok := aggResponse["actor_ids"].(map[string]interface{}); ok {
		if buckets, ok := actorIdsAgg["buckets"].([]interface{}); ok {
			for _, bucket := range buckets {
				if bucketMap, ok := bucket.(map[string]interface{}); ok {
					if actorId, ok := bucketMap["key"].(string); ok {
						actorIds = append(actorIds, actorId)
					}
				}
			}
		}
	}

	return actorIds, nil
}

// printActorDetails searches for actor details by userId and prints the required information
func printActorDetails(actorId string) error {
	// Search for the actor in idp_users
	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"match": {
							"userId.keyword": "%s"
						}
					}
				]
			}
		},
		"size": 1
	}`, TENANT_ID, actorId)

	// Execute the search query
	docs, err := elastic.ExecuteSearchQuery([]string{elastic.IDP_USERS_INDEX}, query)
	if err != nil {
		return fmt.Errorf("failed to execute search query: %w", err)
	}

	if len(docs) == 0 {
		fmt.Printf("No user details found for actor ID: %s\n", actorId)
		return nil
	}

	// Parse the response to get user details
	docBytes, err := json.Marshal(docs[0])
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	var user common.IDPUsersDoc
	if err := json.Unmarshal(docBytes, &user); err != nil {
		return fmt.Errorf("failed to unmarshal user document: %w", err)
	}

	// Print the required information
	fmt.Printf("Email: %s\n", user.Email)
	fmt.Printf("Name: %s\n", user.Name)
	fmt.Printf("Manager Email: %s\n", user.ManagerID)
	fmt.Printf("Manager Name: %s\n", user.ManagerName)
	fmt.Printf("Job Title: %s\n", user.Title)
	fmt.Printf("Department: %s\n", user.Department)

	return nil
}

func main() {
	logger.InitializeLogs("migration", false)

	defaultConf, err := config.InitializeApplicationConfig("application.yml")
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", "application.yml")
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()
	gracefullyShutDown()

	emails := []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}

	// Process each email
	for _, email := range emails {
		fmt.Printf("\n=== Processing email: %s ===\n", email)

		// Step 1: Find user by email and get userId
		userId, err := getUserIdByEmail(email)
		if err != nil {
			fmt.Printf("Error finding user for email %s: %v\n", email, err)
			continue
		}

		if userId == "" {
			fmt.Printf("No user found for email: %s\n", email)
			continue
		}

		fmt.Printf("Found userId: %s\n", userId)

		// Step 2: Search idp_events for activities on this target user and get unique actorIds
		actorIds, err := getActorIdsForTargetUser(userId)
		if err != nil {
			fmt.Printf("Error getting actor IDs for user %s: %v\n", userId, err)
			continue
		}

		if len(actorIds) == 0 {
			fmt.Printf("No events found for target user: %s\n", userId)
			continue
		}

		fmt.Printf("Found %d unique actor IDs\n", len(actorIds))

		// Step 3: For each actorId that's different from the original userId, get user details
		for _, actorId := range actorIds {
			if actorId != userId {
				fmt.Printf("\n--- Actor ID: %s (different from target user) ---\n", actorId)
				err := printActorDetails(actorId)
				if err != nil {
					fmt.Printf("Error getting details for actor %s: %v\n", actorId, err)
				}
			}
		}
	}
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
