2025/07/08 22:23:34 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 22:23:34 Got error connecting to elasticsearch - {Addresses:[http://localhost:9200] Username: Password: CloudID: APIKey: ServiceToken: CertificateFingerprint: Header:map[] CACert:[] RetryOnStatus:[] DisableRetry:false EnableRetryOnTimeout:false MaxRetries:0 CompressRequestBody:false DiscoverNodesOnStart:false DiscoverNodesInterval:0s EnableMetrics:false EnableDebugLogger:false EnableCompatibilityMode:false DisableMetaHeader:false UseResponseCheckOnly:false RetryBackoff:<nil> Transport:<nil> Logger:<nil> Selector:<nil> ConnectionPoolFunc:<nil>} - dial tcp [::1]:9200: connect: connection refused - 
goroutine 1 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x1056394d2, 0x5}, {0x140000aa280, 0x252}, {0x14000037690, 0x2, 0x2})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:119 +0x950
github.com/precize/elastic.ConnectToElasticSearch()
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/elastic/elastic.go:74 +0x68c
main.main()
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/migrations/6_okta_test/main.go:217 +0x140
2025/07/08 22:23:54 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 22:23:54 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "precize-es-cluster",
  "cluster_uuid" : "QgP2-KFsSSmtQYydNJj5nA",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 22:23:55 Index iac_git_commits exists - 
2025/07/08 22:23:55 Index cfstack_templates exists - 
2025/07/08 22:23:55 Index arm_templates exists - 
2025/07/08 22:23:56 Index terraform_resources exists - 
2025/07/08 22:23:56 Index tf_commits exists - 
2025/07/08 22:23:56 Index tf_variables exists - 
2025/07/08 22:23:56 Index resource_context exists - 
2025/07/08 22:23:56 Index text_lookup exists - 
2025/07/08 22:23:57 Index ai_resources exists - 
2025/07/08 22:23:57 Index idp_events exists - 
2025/07/08 22:23:57 Index idp_users exists - 
2025/07/08 22:23:58 Index idp_apps exists - 
2025/07/08 22:23:58 Index idp_groups exists - 
2025/07/08 22:23:58 Index cloud_incidents exists - 
2025/07/08 22:23:59 Index jira_issues exists - 
2025/07/08 22:23:59 Index jira_data exists - 
2025/07/08 22:23:59 Index jira_resources exists - 
2025/07/08 22:23:59 Index precize_creations exists - 
2025/07/08 22:24:00 Index external_cloud_resources exists - 
2025/07/08 22:24:37 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 22:24:38 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "precize-es-cluster",
  "cluster_uuid" : "QgP2-KFsSSmtQYydNJj5nA",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 22:24:38 Index iac_git_commits exists - 
2025/07/08 22:24:38 Index cfstack_templates exists - 
2025/07/08 22:24:39 Index arm_templates exists - 
2025/07/08 22:24:39 Index terraform_resources exists - 
2025/07/08 22:24:39 Index tf_commits exists - 
2025/07/08 22:24:39 Index tf_variables exists - 
2025/07/08 22:24:39 Index resource_context exists - 
2025/07/08 22:24:40 Index text_lookup exists - 
2025/07/08 22:24:40 Index ai_resources exists - 
2025/07/08 22:24:40 Index idp_events exists - 
2025/07/08 22:24:40 Index idp_users exists - 
2025/07/08 22:24:41 Index idp_apps exists - 
2025/07/08 22:24:41 Index idp_groups exists - 
2025/07/08 22:24:41 Index cloud_incidents exists - 
2025/07/08 22:24:41 Index jira_issues exists - 
2025/07/08 22:24:42 Index jira_data exists - 
2025/07/08 22:24:42 Index jira_resources exists - 
2025/07/08 22:24:42 Index precize_creations exists - 
2025/07/08 22:24:42 Index external_cloud_resources exists - 
