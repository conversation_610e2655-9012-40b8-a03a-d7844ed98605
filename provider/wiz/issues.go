package wiz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/provider/wiz/queries"
	"github.com/precize/provider/wiz/types"
)

func CollectIssues(tenantID, token string, wizEnv tenant.WizEnvironment, startTime, endTime, tenantStartTime time.Time) (err error) {

	if endTime.Sub(startTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.WIZ_ISSUE, tenantStartTime)

	if (endTime.Sub(startTime)) > endTime.Sub(defaultTime) {
		startTime = defaultTime
	}

	logger.Print(logger.INFO, "Starting Wiz Cloud Issues collection from "+common.DateTime(startTime)+" to "+common.DateTime(endTime), []string{tenantID})

	var (
		hasNextPage     bool = true
		cursor          string
		batchSize       int = 500
		bulkInsertQuery string
		currentCount    int
	)

	headers := map[string]string{
		"Accept":        "application/json",
		"Authorization": "Bearer " + token,
		"Content-Type":  "application/json",
	}

	for hasNextPage {
		variables := map[string]any{
			"first": batchSize,
			"filterBy": map[string]any{
				"statusChangedAt": map[string]string{
					"before": elastic.DateTime(endTime),
					"after":  elastic.DateTime(startTime),
				},
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.IssuesQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
			return err
		}

		resp, err := SendWizRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
			return err
		}

		var result types.WizIssuesResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
			return err
		}

		processWizIssues(tenantID, result.Data.Issues.Nodes, &bulkInsertQuery, &currentCount, endTime)

		if currentCount > 0 {
			if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of cloud incidents", []string{tenantID}, err, bulkInsertQuery)
				currentCount = 0
				bulkInsertQuery = ""

				continue
			}

			logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Wiz Issues for "+strconv.Itoa(currentCount)+" records", []string{tenantID})

			currentCount = 0
			bulkInsertQuery = ""

		}

		hasNextPage = result.Data.Issues.PageInfo.HasNextPage
		cursor = result.Data.Issues.PageInfo.EndCursor

		time.Sleep(500 * time.Millisecond)
	}

	logger.Print(logger.INFO, "Completed Wiz cloud Issues collection", []string{tenantID})

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.WIZ_ISSUE, endTime)
	return
}

func processWizIssues(tenantID string, issues []types.Issue, bulkInsertQuery *string, currentCount *int, endTime time.Time) error {

	logger.Print(logger.INFO, "Starting Issue processing for batch of "+strconv.Itoa(len(issues))+" records", []string{tenantID})

	for _, issue := range issues {
		if err := processAndStoreWizIssue(tenantID, issue, bulkInsertQuery, currentCount, endTime); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error processing Wiz issue %s", issue.ID),
				[]string{tenantID}, err)
			continue
		}
	}

	logger.Print(logger.INFO, "Completed Issue processing for batch of "+strconv.Itoa(len(issues))+" records", []string{tenantID})

	return nil
}

func processAndStoreWizIssue(tenantID string, issue types.Issue, bulkInsertQuery *string, currentCount *int, endTime time.Time) error {

	incidentDocID := common.GenerateCombinedHashID(tenantID, issue.ID)

	issueTitle := issue.SourceRule.Name

	if len(issueTitle) <= 0 {
		issueTitle = issue.SourceRule.ControlDescription
	}

	if serviceID, ok := common.CspStrToIdIntMap[strings.ToLower(issue.EntitySnapshot.CloudPlatform)]; ok {

		jsonData, err := json.Marshal(issue)
		if err != nil {
			logger.Print(logger.ERROR, "Error Marshalling alert json", err)
			return err
		}

		severity := mapWizSeverity(issue.Severity)
		status := common.INCIDENT_STATUS_OPEN
		if issue.Status == "RESOLVED" {
			status = common.INCIDENT_STATUS_RESOLVED
		}

		resourceID := issue.EntitySnapshot.ProviderID
		if len(resourceID) <= 0 {
			resourceID = issue.EntitySnapshot.ExternalID
		}

		resourceType := issue.EntitySnapshot.NativeType
		if len(resourceType) <= 0 {
			resourceType = issue.EntitySnapshot.Type
		}

		accountID := issue.EntitySnapshot.SubscriptionExternalID
		if len(accountID) <= 0 {
			accountID = issue.EntitySnapshot.ResourceGroupExternalID
		}

		additionalInfo := types.WizAdditionalInfo{
			Type: "Issues",
		}

		additionalInfoJson, err := json.Marshal(additionalInfo)
		if err != nil {
			logger.Print(logger.ERROR, "Error Marshalling alert json", err)
			return err
		}

		additionalInfoString := string(additionalInfoJson)

		incident := common.Incident{
			ID:             incidentDocID,
			AlertID:        issue.ID,
			Issue:          issueTitle,
			EntityID:       resourceID,
			EntityType:     resourceType,
			Source:         common.WIZ_SOURCE,
			IssueSeverity:  severity,
			SourceRisk:     severity,
			CreatedAt:      elastic.DateTime(issue.CreatedAt),
			UpdatedAt:      elastic.DateTime(issue.UpdatedAt),
			ServiceID:      serviceID,
			Category:       issue.Type,
			Description:    issue.SourceRule.ControlDescription,
			Status:         status,
			Stage:          "dc",
			TenantID:       tenantID,
			SourceJson:     string(jsonData),
			InsertTime:     elastic.DateTime(endTime),
			AccountName:    issue.EntitySnapshot.SubscriptionName,
			IsIncident:     false,
			AccountID:      accountID,
			AdditionalData: additionalInfoString,
		}

		if doc, _ := elastic.GetDocument(elastic.CLOUD_INCIDENTS_INDEX, incidentDocID); len(doc) > 0 {

			docBytes, err := json.Marshal(doc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling", []string{tenantID}, err)
				return err
			}

			var existingIncident common.Incident
			if err = json.Unmarshal(docBytes, &existingIncident); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling", []string{tenantID}, err)
				return err
			}

			incident.EntityID = existingIncident.EntityID
			incident.AccountID = existingIncident.AccountID
			incident.CrsID = existingIncident.CrsID
		} else {

			incident.CrsID = common.GenerateCombinedHashIDCaseSensitive(tenantID, strconv.Itoa(serviceID), incident.AccountID, incident.EntityID, incident.EntityType)

			crsDoc, err := elastic.GetDocument(elastic.CLOUD_RESOURCE_STORE_INDEX, incident.CrsID)
			if err != nil {
				return err
			}

			if len(crsDoc) > 0 {

				b, err := json.Marshal(crsDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
					return err
				}

				var crs common.CloudResourceStoreDoc

				if err = json.Unmarshal(b, &crs); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
					return err
				}

				if len(crs.Environment) > 0 {
					incident.Environment = crs.Environment
				}
				if len(crs.Owner) > 0 {
					incident.Owner = crs.Owner
				}
				if len(crs.ResourceName) > 0 {
					incident.ResourceName = crs.ResourceName
				}
				if len(crs.AccountName) > 0 {
					incident.AccountName = crs.AccountName
				}
			}
		}

		incidentBytes, _ := json.Marshal(incident)
		*bulkInsertQuery = *bulkInsertQuery + `{"index": {"_id": "` + incidentDocID + `"}}` + "\n"
		*bulkInsertQuery += string(incidentBytes) + "\n"

		*currentCount++

	}

	return nil
}

func mapWizSeverity(orcaSeverity string) (severity string) {

	switch orcaSeverity {
	case "CRTITICAL":
		severity = common.CRITICAL_RISK
	case "HIGH":
		severity = common.HIGH_RISK
	case "MEDIUM":
		severity = common.MEDIUM_RISK
	case "LOW":
		severity = common.LOW_RISK
	case "INFORMATIONAL":
		severity = common.INFORMATIONAL_RISK
	default:
		severity = common.NOTEVALUATED_RISK
	}

	return
}
