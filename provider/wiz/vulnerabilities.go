package wiz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/provider/wiz/queries"
	"github.com/precize/provider/wiz/types"
	"github.com/precize/transport"
)

func CollectVulnerabilities(tenantID, token string, wizEnv tenant.WizEnvironment, startTime, endTime, tenantStartTime time.Time) (err error) {

	if endTime.Sub(startTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.WIZ_VULN, tenantStartTime)

	if (endTime.Sub(startTime)) > endTime.Sub(defaultTime) {
		startTime = defaultTime
	}

	logger.Print(logger.INFO, "Starting Wiz Cloud Vulnerabilities collection from "+common.DateTime(startTime)+" to "+common.DateTime(endTime), []string{tenantID})

	var (
		hasNextPage     bool = true
		cursor          string
		batchSize       int = 500
		bulkInsertQuery string
		currentCount    int
	)

	headers := map[string]string{
		"Accept":        "application/json",
		"Authorization": "Bearer " + token,
		"Content-Type":  "application/json",
	}

	logger.Print(logger.INFO, "Starting Wiz vulnerability findings collection", []string{tenantID})

	for hasNextPage {
		variables := map[string]any{
			"first": batchSize,
			"filterBy": map[string]any{
				"updatedAt": map[string]any{
					"before": elastic.DateTime(endTime),
					"after":  elastic.DateTime(startTime),
				},
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.VulnerabilityFindingsQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
			return err
		}

		resp, err := transport.SendRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
			return err
		}

		var result types.WizVulnerabilityFindingsResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
			return err
		}

		processWizVulnerabilityFindings(tenantID, result.Data.VulnerabilityFindings.Nodes, &bulkInsertQuery, &currentCount, endTime)

		hasNextPage = result.Data.VulnerabilityFindings.PageInfo.HasNextPage
		cursor = result.Data.VulnerabilityFindings.PageInfo.EndCursor

		if len(bulkInsertQuery) > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of vulnerabilities", []string{tenantID}, err, bulkInsertQuery)

				bulkInsertQuery = ""
				currentCount = 0

				continue
			}

			logger.Print(logger.INFO, "Vulnerability bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})

			bulkInsertQuery = ""
			currentCount = 0
		}

		time.Sleep(500 * time.Millisecond)
	}

	logger.Print(logger.INFO, "Completed Wiz vulnerability findings collection", []string{tenantID}, nil)

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.WIZ_VULN, endTime)
	return
}

func processWizVulnerabilityFindings(tenantID string, findings []types.VulnerabilityFinding, bulkInsertQuery *string, currentCount *int, endTime time.Time) error {

	logger.Print(logger.INFO, "Starting Vulnerability processing for batch of "+strconv.Itoa(len(findings))+" records", []string{tenantID})

	for _, finding := range findings {
		if err := processAndStoreWizVulnerabilityFinding(tenantID, finding, bulkInsertQuery, currentCount, endTime); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error processing Wiz vulnerability finding %s", finding.ID),
				[]string{tenantID}, err)
			continue
		}
	}

	logger.Print(logger.INFO, "Completed Vulnerability processing for batch of "+strconv.Itoa(len(findings))+" records", []string{tenantID})

	return nil
}

func processAndStoreWizVulnerabilityFinding(tenantID string, finding types.VulnerabilityFinding, bulkInsertQuery *string, currentCount *int, endTime time.Time) error {

	incidentDocID := common.GenerateCombinedHashID(tenantID, finding.ID)

	issueTitle := finding.Name

	if len(issueTitle) <= 0 {
		issueTitle = finding.Description
	}

	if serviceID, ok := common.CspStrToIdIntMap[strings.ToLower(finding.VulnerableAsset.CloudPlatform)]; ok {

		jsonData, err := json.Marshal(finding)
		if err != nil {
			logger.Print(logger.ERROR, "Error Marshalling alert json", err)
			return err
		}

		severity := mapWizSeverity(finding.Severity)
		status := common.INCIDENT_STATUS_OPEN
		if finding.Status == "RESOLVED" {
			status = common.INCIDENT_STATUS_RESOLVED
		}

		resourceID := finding.VulnerableAsset.ProviderUniqueId
		if len(resourceID) <= 0 {
			resourceID = finding.VulnerableAsset.CloudProviderURL
		}

		resourceType := finding.VulnerableAsset.NativeType
		if len(resourceType) <= 0 {
			resourceType = finding.VulnerableAsset.Type
		}

		accountID := finding.VulnerableAsset.SubscriptionExternalId

		recommendationFindings := make([]common.RecommendationFindings, 0)

		if strings.Contains(strings.ToLower(finding.Name), "cve") {

			var recommendationFinding common.RecommendationFindings

			recommendationFinding.Description = finding.CVEDescription
			recommendationFinding.ID = finding.Name
			recommendationFinding.Score = float32(finding.Score)
			recommendationFinding.Severity = finding.Severity

			recommendationFindings = append(recommendationFindings, recommendationFinding)
		}

		additionalInfo := types.WizAdditionalInfo{
			IsInternetFacing:       finding.VulnerableAsset.HasLimitedInternetExposure || finding.VulnerableAsset.HasWideInternetExposure,
			RecommendationFindings: recommendationFindings,
			Type:                   "Vulnerability",
			HasExploit:             finding.HasExploit,
			HasCisaKevExploit:      finding.HasCisaKevExploit,
		}

		additionalInfoJson, err := json.Marshal(additionalInfo)
		if err != nil {
			logger.Print(logger.ERROR, "Error Marshalling alert json", err)
			return err
		}

		additionalInfoString := string(additionalInfoJson)

		incident := common.Incident{
			ID:                 incidentDocID,
			AlertID:            finding.ID,
			Issue:              issueTitle,
			EntityID:           resourceID,
			EntityType:         resourceType,
			Source:             common.WIZ_SOURCE,
			IssueSeverity:      severity,
			SourceRisk:         severity,
			IssueSeverityScore: float32(finding.Score),
			CreatedAt:          elastic.DateTime(finding.FirstDetectedAt),
			UpdatedAt:          elastic.DateTime(finding.LastDetectedAt),
			ServiceID:          serviceID,
			Category:           "Vulnerability",
			Description:        finding.Description,
			Status:             status,
			Stage:              "dc",
			TenantID:           tenantID,
			SourceJson:         string(jsonData),
			InsertTime:         elastic.DateTime(endTime),
			AccountName:        finding.VulnerableAsset.SubscriptionName,
			IsIncident:         true,
			AccountID:          accountID,
			AdditionalData:     additionalInfoString,
		}

		if doc, _ := elastic.GetDocument(elastic.CLOUD_INCIDENTS_INDEX, incidentDocID); len(doc) > 0 {

			docBytes, err := json.Marshal(doc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling", []string{tenantID}, err)
				return err
			}

			var existingIncident common.Incident
			if err = json.Unmarshal(docBytes, &existingIncident); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling", []string{tenantID}, err)
				return err
			}

			incident.EntityID = existingIncident.EntityID
			incident.AccountID = existingIncident.AccountID
			incident.CrsID = existingIncident.CrsID
		} else {

			incident.CrsID = common.GenerateCombinedHashIDCaseSensitive(tenantID, strconv.Itoa(serviceID), incident.AccountID, incident.EntityID, incident.EntityType)

			crsDoc, err := elastic.GetDocument(elastic.CLOUD_RESOURCE_STORE_INDEX, incident.CrsID)
			if err != nil {
				return err
			}

			if len(crsDoc) > 0 {

				b, err := json.Marshal(crsDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
					return err
				}

				var crs common.CloudResourceStoreDoc

				if err = json.Unmarshal(b, &crs); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
					return err
				}

				if len(crs.Environment) > 0 {
					incident.Environment = crs.Environment
				}
				if len(crs.Owner) > 0 {
					incident.Owner = crs.Owner
				}
				if len(crs.ResourceName) > 0 {
					incident.ResourceName = crs.ResourceName
				}
				if len(crs.AccountName) > 0 {
					incident.AccountName = crs.AccountName
				}
			}
		}

		incidentBytes, _ := json.Marshal(incident)

		*bulkInsertQuery = *bulkInsertQuery + `{"index": {"_id": "` + incidentDocID + `"}}` + "\n"
		*bulkInsertQuery += string(incidentBytes) + "\n"
		*currentCount++
	}

	return nil
}
