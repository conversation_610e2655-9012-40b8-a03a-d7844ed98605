package queries

const ConfigurationFindingsQuery = `
query CloudConfigurationFindingsPage(
  $filterBy: ConfigurationFindingFilters
  $first: Int
  $after: String
  $orderBy: ConfigurationFindingOrder
) {
  configurationFindings(
    filterBy: $filterBy
    first: $first
    after: $after
    orderBy: $orderBy
  ) {
    nodes {
      id
      targetExternalId
      targetObjectProviderUniqueId
      firstSeenAt
      severity
      result
      status
      remediation
      resource {
        id
        providerId
        name
        nativeType
        type
        region
        subscription {
          id
          name
          externalId
          cloudProvider
        }
        projects {
          id
          name
          riskProfile {
            businessImpact
          }
        }
        tags {
          key
          value
        }
      }
      rule {
        id
        graphId
        name
        description
        remediationInstructions
        functionAsControl
        builtin
      }
      securitySubCategories {
        id
        title
        category {
          id
          name
          framework {
            id
            name
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
`
