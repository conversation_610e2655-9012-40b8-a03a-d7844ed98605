package wiz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/provider/wiz/queries"
	"github.com/precize/provider/wiz/types"
	"github.com/precize/transport"
)

func CollectRscConfigFindings(tenantID, serviceID, token string, wizEnv tenant.WizEnvironment, startTime, endTime time.Time) (err error) {
	var (
		hasNextPage     bool = true
		cursor          string
		batchSize       int = 1000
		bulkInsertQuery string
		currentCount    int
		maxRecords      = 1000
	)

	headers := map[string]string{
		"Accept":        "application/json",
		"Authorization": "Bearer " + token,
		"Content-Type":  "application/json",
	}

	logger.Print(logger.INFO, "Starting Wiz cloud configuration findings collection", []string{tenantID}, nil)

	for hasNextPage {
		variables := map[string]any{
			"first": batchSize,
			"filterBy": map[string]any{
				"includeDeleted": true,
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.ConfigurationFindingsQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
			return err
		}

		resp, err := transport.SendRequest("POST", wizEnv.URL, headers, nil, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
			return err
		}

		var result types.WizConfigFindingsResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
			return err
		}

		processWizConfigFindings(tenantID, result.Data.ConfigurationFindings.Nodes, &bulkInsertQuery, &currentCount)

		hasNextPage = result.Data.ConfigurationFindings.PageInfo.HasNextPage
		cursor = result.Data.ConfigurationFindings.PageInfo.EndCursor

		if currentCount > maxRecords {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of cloud configurations", []string{tenantID}, err)
				break
			}

			logger.Print(logger.INFO, "Cloud Configuration bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
			currentCount = 0
			bulkInsertQuery = ""
		}

		time.Sleep(500 * time.Millisecond)
	}

	if currentCount > 0 {
		if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			logger.Print(logger.ERROR, "Error in bulk insertion of cloud configurations", []string{tenantID}, err)
			return
		}

		logger.Print(logger.INFO, "Cloud Configuration bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	logger.Print(logger.INFO, "Completed Wiz cloud configuration findings collection", []string{tenantID}, nil)

	return
}

func processWizConfigFindings(tenantID string, findings []types.ConfigurationFinding, bulkInsertQuery *string, currentCount *int) error {
	for _, finding := range findings {
		if err := processAndStoreWizConfigFinding(tenantID, finding, bulkInsertQuery, currentCount); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error processing Wiz configuration finding %s", finding.ID),
				[]string{tenantID}, err)
			continue
		}
	}
	return nil
}

func processAndStoreWizConfigFinding(tenantID string, finding types.ConfigurationFinding, bulkInsertQuery *string, currentCount *int) error {
	configDoc := common.Incident{}

	// TODO: Normalize based on api output

	docID := tenantID + ":" + finding.ID
	configBytes, _ := json.Marshal(configDoc)
	*bulkInsertQuery = *bulkInsertQuery + `{"index": {"_id": "` + docID + `"}}` + "\n"
	*bulkInsertQuery += string(configBytes) + "\n"

	*currentCount++

	return nil
}
