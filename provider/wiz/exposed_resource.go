package wiz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	wizCommon "github.com/precize/provider/wiz/common"
	"github.com/precize/provider/wiz/queries"
	"github.com/precize/provider/wiz/types"
	"github.com/precize/transport"
)

func CollectExposedResources(tenantID, token string, wizEnv tenant.WizEnvironment, startTime, endTime, tenantStartTime time.Time, tenantData tenant.TenantData) {
	if endTime.Sub(startTime) < resourceBufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.WIZ_RESOURCE, tenantStartTime)

	if (endTime.Sub(startTime)) > endTime.Sub(defaultTime) {
		startTime = defaultTime
	}

	logger.Print(logger.INFO, "Starting Wiz Exposed Resources collection from "+common.DateTime(startTime)+" to "+common.DateTime(endTime), []string{tenantID})

	var (
		hasNextPage             bool = true
		cursor                  string
		batchSize               int = 500
		resourceNetworkMetaData     = make(map[string]map[string]any)
	)

	enabledCsps := make(map[string]bool)

	if len(tenantData.AWSAccounts.Environment) > 0 || true {
		enabledCsps["aws"] = true
	}

	if len(tenantData.AzureAccounts.Environment) > 0 || true {
		enabledCsps["azure"] = true
	}

	if len(tenantData.GCPAccounts.Environment) > 0 || true {
		enabledCsps["gcp"] = true
	}

	headers := map[string]string{
		"accept":        "application/json",
		"authorization": "Bearer " + token,
		"content-Type":  "application/json",
	}

	logger.Print(logger.INFO, "Starting wiz exposed network cloud resources collection", []string{tenantID})

	for hasNextPage {
		variables := map[string]any{
			"first": batchSize,
			"filterBy": map[string]any{
				"type": []string{
					"PUBLIC_INTERNET",
				},
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.ExposedResourceQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
		}

		resp, err := transport.SendRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
		}

		var result types.WizNetworkExposureResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
		}

		processExposedResources(tenantID, enabledCsps, result.Data.NetworkResources.Nodes, resourceNetworkMetaData, endTime)

		hasNextPage = result.Data.NetworkResources.PageInfo.HasNextPage
		cursor = result.Data.NetworkResources.PageInfo.EndCursor

		logger.Print(logger.INFO, fmt.Sprintf("Collected %d resources from Wiz", len(result.Data.NetworkResources.Nodes)),
			[]string{tenantID})

		time.Sleep(500 * time.Millisecond)
	}

	logger.Print(logger.INFO, "Completed Wiz cloud exposed resources collection", []string{tenantID})

	return
}

func processExposedResources(tenantID string, enabledCsps map[string]bool, networkExposures []types.NetworkExposure, resourceNetworkMetaData map[string]map[string]any, endTime time.Time) {

	resourceIDs := make([]string, 0)
	collectedAt := endTime.UnixMilli()
	bulkExtCloudResourceRequest := ""
	count := 0

	for _, networkExposure := range networkExposures {

		if cloudPlatform, ok := networkExposure.ExposedEntity.Properties["cloudPlatform"].(string); ok {
			if enabledCsps[strings.ToLower(cloudPlatform)] {

				serviceCode := common.CspStrToIdIntMap[strings.ToLower(cloudPlatform)]

				resourceID := ""
				resourceType := ""
				region := ""
				accountID := ""

				if rscID, ok := networkExposure.ExposedEntity.Properties["providerUniqueId"].(string); ok {
					resourceID = rscID
				}

				if rscID, ok := networkExposure.ExposedEntity.Properties["externalId"].(string); ok {
					resourceID = rscID
				}

				if rscType, ok := networkExposure.ExposedEntity.Properties["nativeType"].(string); ok {
					resourceType = rscType
				}

				if len(resourceType) <= 0 {
					resourceType = networkExposure.ExposedEntity.Type
				}

				if reg, ok := networkExposure.ExposedEntity.Properties["region"].(string); ok {
					region = reg
				}

				if accID, ok := networkExposure.ExposedEntity.Properties["subscriptionExternalId"].(string); ok {
					accountID = accID
				}

				resourceID, resourceType = wizCommon.FetchResourceIDAndType(networkExposure.ExposedEntity.Properties, accountID, region, resourceID, resourceType, networkExposure.ExposedEntity.Name, networkExposure.ExposedEntity.Type)

				resourceDocID := common.GenerateCombinedHashIDCaseSensitive(tenantID, fmt.Sprintf("%d", serviceCode), accountID, resourceID, resourceType)

				openPorts := []string{}
				if ports, ok := networkExposure.ExposedEntity.Properties["validatedOpenPorts"]; ok && ports != nil {
					openPorts = common.ConvertToStringSlice(ports)
				}

				networkMetadata := map[string]any{
					"portAdditionalInfo": map[string]any{
						"priorityConfigs": map[string]any{
							"Public IP Address": networkExposure.DestinationIPRange,
							"Open Ports":        formatOpenPorts(openPorts),
						},
						"accountId":         accountID,
						"ipScanTime":        collectedAt,
						"openPorts":         formatOpenPortDetails(openPorts),
						"collectedAt":       collectedAt,
						"ipAddress":         networkExposure.DestinationIPRange,
						"relatedEntityPath": resourceID,
						"region":            region,
					},
					"openToAllInternet":        networkExposure.ExposedEntity.Properties["openToAllInternet"],
					"openToInternet":           networkExposure.ExposedEntity.Properties["accessibleFromInternet"],
					"hasAccessToSensitiveData": networkExposure.ExposedEntity.Properties["hasAccessToSensitiveData"],
					"hasSensitiveData":         networkExposure.ExposedEntity.Properties["hasSensitiveData"],
				}

				resourceIDs = append(resourceIDs, resourceDocID)
				resourceNetworkMetaData[resourceDocID] = networkMetadata
			}
		}

	}

	if len(resourceIDs) > 0 {
		var (
			searchAfter any
			extCrQuery  = `{"query":{"bool":{"must":[{"match":{"source.keyword":"` + common.WIZ_SOURCE + `"}},{"terms":{"id.keyword":["` + strings.Join(resourceIDs, `","`) + `"]}},{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`
		)

		for {
			crDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.EXTERNAL_CLOUD_RESOURCES_INDEX}, extCrQuery, searchAfter)
			if err != nil {
				return
			}

			if len(crDocs) > 0 {
				searchAfter = sortResponse
			} else {
				break
			}

			for id, crDoc := range crDocs {

				if entityJsonStr, ok := crDoc["entityJson"].(string); ok {

					entityJson := make(map[string]any)
					if err := json.Unmarshal([]byte(entityJsonStr), &entityJson); err != nil {
						logger.Print(logger.ERROR, "Error unmarshalling cr json", []string{tenantID}, err)
						continue
					}

					if networkExposuresMap, ok := resourceNetworkMetaData[id]; ok {
						for k, v := range networkExposuresMap {
							entityJson[k] = v
						}
					}

					entityJsonBytes, err := json.Marshal(entityJson)
					if err != nil {
						logger.Print(logger.ERROR, "Error marshalling updated entityJson", []string{tenantID}, err)
						continue
					}

					updateMeta := map[string]map[string]string{"update": {"_id": id}}
					updateDoc := map[string]map[string]string{"doc": {"entityJson": string(entityJsonBytes)}}

					metaLine, err := json.Marshal(updateMeta)
					if err != nil {
						logger.Print(logger.ERROR, "Error marshalling update metadata", []string{tenantID}, err)
						continue
					}

					docLine, err := json.Marshal(updateDoc)
					if err != nil {
						logger.Print(logger.ERROR, "Error marshalling doc update", []string{tenantID}, err)
						continue
					}

					bulkExtCloudResourceRequest += string(metaLine) + "\n" + string(docLine) + "\n"
					count += 1
				}
			}
		}

		if len(bulkExtCloudResourceRequest) > 0 {

			if err := elastic.BulkDocumentsAPI(tenantID, elastic.EXTERNAL_CLOUD_RESOURCES_INDEX, bulkExtCloudResourceRequest); err != nil {
				logger.Print(logger.ERROR, "Error in bulk update of cloud resources", []string{tenantID}, err, bulkExtCloudResourceRequest)
				return
			}

			logger.Print(logger.INFO, "Cloud resources bulk update API Successful for ", []string{tenantID}, count)
		}
	}
}

func formatOpenPorts(ports []string) []string {
	formattedPorts := make([]string, len(ports))
	for i, port := range ports {
		formattedPorts[i] = fmt.Sprintf("Port : %s, App Name : Unknown, Version : Unknown, Confirmed Time : %s",
			port, time.Now().Format(time.RFC3339Nano))
	}
	return formattedPorts
}

func formatOpenPortDetails(ports []string) []map[string]any {
	portDetails := make([]map[string]any, len(ports))
	for i, port := range ports {
		portDetails[i] = map[string]any{
			"ports":                 port,
			"version":               "Unknown",
			"appName":               "Unknown",
			"url":                   nil,
			"publiclyAccessibleUrl": false,
			"portScanConfirmedTime": time.Now().Unix(),
			"vulnerability":         []any{},
		}
	}
	return portDetails
}
