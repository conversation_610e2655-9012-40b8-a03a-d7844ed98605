package orca

import (
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"

	commonExtCr "github.com/precize/common/ext_cr"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

var serviceToPropertyMap = map[string]any{
	"storage": []map[string]any{
		{
			"key":  "storage_class",
			"path": "storage.storage_class",
		},
		{
			"key":  "content",
			"path": "model.data.content",
		},
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpStorageBucket",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"vm": []map[string]any{
		{
			"key":  "content",
			"path": "model.data.content",
		},
		{
			"key":  "additionalDetails",
			"path": "model.data.compute",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"gcpapikey": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpApiKey",
		},
	},
	"cloudaccount": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.cloudaccount",
		},
	},
	"gcpbigquerydataset": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpBigqueryDataset",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"gcpbigtablecluster": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpBigtableCluster",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpbigtableinstance": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpBigtableInstance",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpsecretmanagersecret": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpSecretManagerSecret",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpspannerinstance": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpSpannerInstance",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpvpngateway": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpVpnGateway",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"newip": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.Address",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "locationDetails",
			"path": "model.data.NewIP",
		},
	},
	"gcpschedulesnapshot": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpScheduleSnapshot",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpcloudrunservice": []map[string]any{
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"gcpsqlinstance": []map[string]any{
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
		{
			"key":  "blastRadius",
			"path": "model.data.inventory.BlastRadius",
		},
	},
	"gcpvpntunnel": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpVpnTunnel",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpsecretmanagersecretversion": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpSecretManagerSecretVersion",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpcomputetargetpool": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpComputeTargetPool",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpfilestoreinstance": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpFileStoreInstance",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"gcploadbalancerbackendservice": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpLoadBalancerBackendService",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "exposure",
			"path": "model.data.inventory.exposure",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"function": []map[string]any{
		{
			"key":  "content",
			"path": "model.data.content",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"gcpcertificate": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpCertificate",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpdnsmanagedzone": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpDnsManagedZone",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcpvpcfirewallrule": []map[string]any{
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"vmimage": []map[string]any{
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
		{
			"key":  "content",
			"path": "model.data.content",
		},
		{
			"key":  "custom",
			"path": "model.data.GcpVmImage.IsCustom",
		},
	},
	"gcpredisinstance": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpRedisInstance",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"gcpkmskey": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpKmsKey",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"gcploadbalancer": []map[string]any{
		{
			"key":  "backendServices",
			"path": "model.data.GcpLoadBalancer.BackendServices",
		},
		{
			"key":  "urlMapData",
			"path": "model.data.GcpLoadBalancer.UrlMapData",
		},
		{
			"key":  "hasInsecureSslCiphers",
			"path": "model.data.GcpLoadBalancer.HasInsecureSslCiphers",
		},
	},
	"gcpmemcachecluster": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpMemCacheCluster",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"containerimage": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.ContainerImage",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
		{
			"key":  "content",
			"path": "model.data.content",
		},
		{
			"key":  "compute",
			"path": "model.data.Compute",
		},
		{
			"key":  "gcrImage",
			"path": "model.data.GcpGcrImage",
		},
		{
			"key":  "imageDetails",
			"path": "containerimage",
		},
	},
	"gcploadbalancerforwardingrule": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpLoadBalancerForwardingRule",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
		{
			"key":  "exposure",
			"path": "model.data.inventory.exposure",
		},
		{
			"key":  "observations",
			"path": "model.data.inventory.observations",
		},
	},
	"gke": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.Compute",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
		{
			"key":  "content",
			"path": "model.data.content",
		},
	},
	"gcpbigquerytable": []map[string]any{
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
	},
	"gcpgkecluster": []map[string]any{
		{
			"key":  "authenticatorGroupsConfig",
			"path": "model.data.GcpGkeCluster.AuthenticatorGroupsConfig",
		},
		{
			"key":  "servicesIpv4Cidr",
			"path": "model.data.GcpGkeCluster.ServicesIpv4Cidr",
		},
		{
			"key":  "releaseChannel",
			"path": "model.data.GcpGkeCluster.ReleaseChannel",
		},
		{
			"key":  "currentNodeCount",
			"path": "model.data.GcpGkeCluster.CurrentNodeCount",
		},
		{
			"key":  "masterAuthorizedNetworksConfig",
			"path": "model.data.GcpGkeCluster.MasterAuthorizedNetworksConfig",
		},
		{
			"key":  "isBasicAuthEnabled",
			"path": "model.data.GcpGkeCluster.IsBasicAuthEnabled",
		},
		{
			"key":  "isMasterVersionValid",
			"path": "model.data.GcpGkeCluster.IsMasterVersionValid",
		},
		{
			"key":  "isNetworkPolicyEnabled",
			"path": "model.data.GcpGkeCluster.IsNetworkPolicyEnabled",
		},
		{
			"key":  "isPodSecurityPolicyEnabled",
			"path": "model.data.GcpGkeCluster.IsPodSecurityPolicyEnabled",
		},
		{
			"key":  "certificateBasedAuthentication",
			"path": "model.data.GcpGkeCluster.CertificateBasedAuthentication",
		},
		{
			"key":  "maintenancePolicy",
			"path": "model.data.GcpGkeCluster.MaintenancePolicy",
		},
		{
			"key":  "alphaClusterEnabled",
			"path": "model.data.GcpGkeCluster.AlphaClusterEnabled",
		},
		{
			"key":  "monitoringService",
			"path": "model.data.GcpGkeCluster.MonitoringService",
		},
		{
			"key":  "ipAllocationPolicy",
			"path": "model.data.GcpGkeCluster.IpAllocationPolicy",
		},
		{
			"key":  "databaseEncryption",
			"path": "model.data.GcpGkeCluster.DatabaseEncryption",
		},
		{
			"key":  "networkConfig",
			"path": "model.data.GcpGkeCluster.NetworkConfig",
		},
		{
			"key":  "addonsConfig",
			"path": "model.data.GcpGkeCluster.AddonsConfig",
		},
		{
			"key":  "privateClusterConfig",
			"path": "model.data.GcpGkeCluster.PrivateClusterConfig",
		},
	},
	"gcpspannerdatabase": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.GcpSpannerDatabase",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
	},
	"container": []map[string]any{
		{
			"key":  "additionalDetails",
			"path": "model.data.Container",
		},
		{
			"key":  "state",
			"path": "asset_state",
		},
		{
			"key":  "isOpenToInternet",
			"path": "model.data.inventory.IsInternetFacing",
		},
		{
			"key":  "compute",
			"path": "model.data.Compute",
		},
	},
}

var orcaToPrecizeServiceMap = map[string]string{
	// keys should be in lower case
	"storage":                            "CloudStorage",
	"vm":                                 "VMInstance",
	"gcpapikey":                          "ApiKey",
	"cloudaccount":                       "Project",
	"gcpbigquerydataset":                 "BigQueryDataset",
	"gcpbigtablecluster":                 "BigTableCluster",
	"gcpbigtableinstance":                "BigTableInstance",
	"gcpsecretmanagersecret":             "SecretManagerSecret",
	"gcpspannerinstance":                 "SpannerInstance",
	"gcpvpngateway":                      "VPNGateway",
	"newip":                              "ipDetails",
	"gcpschedulesnapshot":                "ResourcePolicy",
	"gcpcloudrunservice":                 "CloudRunService",
	"gcpsqlinstance":                     "SQLDatabase",
	"gcpvpntunnel":                       "VPNTunnel",
	"gcpsecretmanagersecretversion":      "SecretManagerSecretVersion",
	"gcpcomputetargetpool":               "TargetPool",
	"gcpfilestoreinstance":               "FileStoreInstance",
	"gcploadbalancerbackendservice":      "BackendService",
	"function":                           "Function",
	"gcpcertificate":                     "SslCertificate",
	"gcpdnsmanagedzone":                  "DnsManagedZone",
	"gcpvpcfirewallrule":                 "FirewallRule",
	"vmimage":                            "Image",
	"gcpredisinstance":                   "RedisInstance",
	"gcpkmskey":                          "CryptoKey",
	"gcploadbalancer":                    "LBTargetHttpsProxy",
	"gcpmemcachecluster":                 "MemCacheInstance",
	"containerimage":                     "DockerImage",
	"gcploadbalancerforwardingrule":      "LoadBalancer",
	"gke":                                "VMInstance",
	"gcpbigquerytable":                   "BigQueryTable",
	"gcpgkecluster":                      "GKECluster",
	"gcpspannerdatabase":                 "SpannerDatabase",
	"container":                          "VMContainer",
	"serverlesscontainer":                "",
	"gcpcloudarmorsecuritypolicyrule":    "",
	"gcpcloudarmorsecuritypolicy":        "",
	"gcpcloudrunrevision":                "",
	"gcpcomputeinstancegroup":            "",
	"gcpdmsdestinationconnectionprofile": "",
	"gcpdmsjob":                          "",
	"gcpdmssourceconnectionprofile":      "",
	"gcpgarrepository":                   "",
	"gcpiampolicy":                       "",
	"gcpiamrole":                         "",
	"gcpiamserviceaccountkey":            "",
	"gcpipaddress":                       "",
	"gcploggingmetric":                   "",
	"gcploggingsink":                     "",
	"gcpmonitoringalertpolicy":           "",
	"gcppubsubsubscription":              "",
	"gcppubsubtopic":                     "",
	"gcpsslpolicy":                       "",
	"gcpvmdisk":                          "",
	"gcpiamserviceaccount":               "",
	"gcpvmsnapshot":                      "",
	"gcpvpcfirewallruleippermissions":    "",
	"gcpvpcpeeringconnection":            "",
	"gcpvpcsubnet":                       "",
	"gcpvpc":                             "",
	"newdomain":                          "",
}

func convertKeysToLowerCase(data any) map[string]any {

	if data == nil {
		return make(map[string]any)
	}

	v := reflect.ValueOf(data)
	if v.Kind() != reflect.Map {
		return make(map[string]any)
	}

	result := make(map[string]any)
	for _, key := range v.MapKeys() {
		keyStr := fmt.Sprintf("%v", key.Interface())
		lowerKey := strings.ToLower(keyStr)
		value := v.MapIndex(key).Interface()
		result[lowerKey] = convertValue(value)
	}
	return result
}

func convertValue(data any) any {
	v := reflect.ValueOf(data)

	switch v.Kind() {
	case reflect.Map:
		return convertKeysToLowerCase(data)

	case reflect.Slice, reflect.Array:
		result := make([]any, v.Len())
		for i := 0; i < v.Len(); i++ {
			result[i] = convertValue(v.Index(i).Interface())
		}
		return result

	default:
		return data
	}
}

func GetResourceDetailsForContainerImage(assetName string) (projectName string, repoName string, version string) {
	assetParts := strings.Split(assetName, "/")

	if len(assetParts) != 2 {
		return "", "", ""
	}

	projectName = assetParts[0]

	repoNameVersionName := strings.Split(assetParts[1], ":")

	if len(repoNameVersionName) != 2 {
		return "", "", ""
	}

	repoName = repoNameVersionName[0]
	version = repoNameVersionName[1]

	return projectName, repoName, version
}

func GetPrecizeEntityIDForContainerImage(projectName, repoName, version, resourceType, tenantID string) (entityID string, err error) {
	entityID = strings.ToLower(entityID)

	query := `
	{
		"query": {
		  "bool": {
			"must": [
			  {
				"term": {
				  "entityType.keyword": "` + resourceType + `"
				}
			  },
			  {
				"term": {
				  "tenantId.keyword": "` + tenantID + `"
				}
			  },
			  {
				"wildcard": {
				  "entityId.keyword": "*` + projectName + `*"
				}
			  },
			  {
				"wildcard": {
				  "entityId.keyword": "*` + repoName + `*"
				}
			  },
			  {
				"match": {
				  "entityJson": "` + version + `"
				}
			  }
			],
			"must_not": [],
			"should": []
		  }
		},
		"size": 1
	  }
	  `

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCES_INDEX}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource store", []string{tenantID}, err)
		return
	}

	for _, doc := range docs {

		if entityID, ok := doc["entityId"].(string); ok {
			return entityID, nil
		}
	}

	return "", nil
}

func contains(slice []any, item any) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func flattenJSON(jsonData map[string]any, flattened map[string]any, prefix string) {
	for key, value := range jsonData {
		fullKey := strings.ToLower(key)
		if prefix != "" {
			fullKey = prefix + "." + fullKey
		}

		switch v := value.(type) {
		case map[string]any:
			flattenJSON(v, flattened, fullKey)
		case []any:
			for i, item := range v {
				arrayKey := fmt.Sprintf("%s[%d]", fullKey, i)
				if nestedMap, ok := item.(map[string]any); ok {
					flattenJSON(nestedMap, flattened, arrayKey)
				} else {
					processedValue := processValue(item)
					addToFlattened(flattened, arrayKey, processedValue)
				}
			}
		default:
			processedValue := processValue(v)
			addToFlattened(flattened, fullKey, processedValue)
		}
	}
}

func processValue(value any) any {
	if str, ok := value.(string); ok {
		return strings.ToLower(strings.ReplaceAll(str, " ", ""))
	}
	return value
}

func addToFlattened(flattened map[string]any, key string, value any) {
	if existingValue, exists := flattened[key]; exists {
		switch existing := existingValue.(type) {
		case []any:
			if !contains(existing, value) {
				flattened[key] = append(existing, value)
			}
		default:
			if !contains([]any{existing}, value) {
				flattened[key] = []any{existing, value}
			}
		}
	} else {
		flattened[key] = value
	}
}

func getValue(flattenedData map[string]any, originalData map[string]any, path string) (any, bool) {

	normalizedPath := strings.ToLower(path)
	if value, exists := flattenedData[normalizedPath]; exists {
		return value, true
	}

	parts := strings.Split(normalizedPath, ".")
	var current any = originalData

	for _, part := range parts {
		openBracket := strings.Index(part, "[")
		closeBracket := strings.Index(part, "]")

		if openBracket > 0 && closeBracket > openBracket {

			keyPart := part[:openBracket]
			indexPart := part[openBracket+1 : closeBracket]

			currentMap, ok := current.(map[string]any)
			if !ok {
				return nil, false
			}

			sliceVal, exists := currentMap[keyPart]
			if !exists {
				return nil, false
			}

			slice, ok := sliceVal.([]any)
			if !ok {
				return nil, false
			}

			index, err := strconv.Atoi(indexPart)
			if err != nil || index < 0 || index >= len(slice) {
				return nil, false
			}

			current = slice[index]

		} else {

			currentMap, ok := current.(map[string]any)
			if !ok {
				return nil, false
			}

			value, exists := currentMap[part]
			if !exists {
				return nil, false
			}
			current = value
		}
	}

	return current, true
}

func normalizeOrcaAssetData(assetsDataMap map[string]any, precizeAsset *commonExtCr.ExternalCloudResource, parentRscNameToID map[string]string, unsupportedData *UnsupportedData) (skip bool, err error) {

	flattenedData := make(map[string]any)
	flattenJSON(assetsDataMap, flattenedData, "")

	assetsDataMap = convertKeysToLowerCase(assetsDataMap)

	if assetName, ok := assetsDataMap["asset_name"].(string); ok {
		precizeAsset.EntityID = assetName
		precizeAsset.ResourceName = assetName
	}

	if assetType, ok := assetsDataMap["asset_type"].(string); ok {
		if precizeResourceType, ok := orcaToPrecizeServiceMap[strings.ToLower(assetType)]; ok {
			if len(precizeResourceType) <= 0 {
				return true, nil
			}
			precizeAsset.EntityType = precizeResourceType
		} else {
			unsupportedData.AssetTypes[assetType] = struct{}{}
			return true, nil
		}
	}

	if region, exists := getValue(flattenedData, assetsDataMap, "model.data.inventory.region"); exists {
		if regionStr, ok := region.(string); ok {
			precizeAsset.Region = strings.ToLower(regionStr)
		}
	}

	if accountName, ok := assetsDataMap["account_name"].(string); ok {
		precizeAsset.AccountName = accountName
		if accID, ok := parentRscNameToID[accountName]; ok {
			precizeAsset.AccountID = accID
		} else {
			logger.Print(logger.ERROR, "Account ID not present for account name", []string{precizeAsset.TenantID}, accountName)
			return true, nil
		}
	}

	formattedEntityID := buildEntityID(flattenedData, assetsDataMap, precizeAsset, parentRscNameToID, unsupportedData)
	if len(formattedEntityID) > 0 {
		precizeAsset.EntityID = strings.ToLower(formattedEntityID)
	}

	if len(precizeAsset.Region) <= 0 {
		if zones, ok := assetsDataMap["model.data.inventory.zones"].([]any); ok {
			if len(zones) > 0 {
				if zone, ok := zones[0].(string); ok {
					precizeAsset.Region = strings.ToLower(zone)
				}
			}
		}
	}

	buildEntityJson(flattenedData, assetsDataMap, precizeAsset, unsupportedData)

	if createdAt, ok := getValue(flattenedData, assetsDataMap, "state.created_at"); ok {
		if createdAtStr, ok := createdAt.(string); ok {
			if parsedTime, err := time.Parse(elastic.ORCA_DATE_FORMAT, strings.ToUpper(createdAtStr)); err == nil {
				precizeAsset.CreatedDate = parsedTime.UTC().Format(elastic.DATE_FORMAT)
			}
		}
	}

	if updatedAt, ok := getValue(flattenedData, assetsDataMap, "model.data.inventory.updatedtime"); ok {
		if updatedAtStr, ok := updatedAt.(string); ok {
			if parsedTime, err := time.Parse(elastic.ORCA_DATE_FORMAT, strings.ToUpper(updatedAtStr)); err == nil {
				precizeAsset.UpdatedDate = parsedTime.UTC().Format(elastic.DATE_FORMAT)
			}
		}
	}

	if tagsList, ok := assetsDataMap["tags_list"].([]any); ok {
		precizeAsset.Tags = []commonExtCr.Tag{}
		for _, tagItem := range tagsList {
			if tagMap, ok := tagItem.(map[string]any); ok {
				var key, value string

				if k, ok := tagMap["key"].(string); ok {
					key = k
				}

				if v, ok := tagMap["value"].(string); ok {
					value = v
				}

				if key != "" || value != "" {
					precizeAsset.Tags = append(precizeAsset.Tags, commonExtCr.Tag{
						Key:   key,
						Value: value,
					})
				}
			}
		}

		precizeAsset.TagsCount = len(precizeAsset.Tags)
	}

	precizeAsset.Deleted = false
	if status, ok := getValue(flattenedData, assetsDataMap, "model.data.Inventory.State"); ok {
		if statusStr, ok := status.(string); ok {
			if strings.Contains(strings.ToLower(statusStr), "delet") {
				precizeAsset.Deleted = true
			}
		}
	}

	return false, nil
}

func buildEntityID(flattenedData map[string]any, assetsDataMap map[string]any, precizeAsset *commonExtCr.ExternalCloudResource, parentRscNameToID map[string]string, unsupportedData *UnsupportedData) (entityID string) {

	entityType, exisits := getValue(flattenedData, assetsDataMap, "asset_type")
	if !exisits {
		return
	}

	switch entityType {
	case VM_RESOURCE_TYPE:
		if clusterSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpVmInstance.selfLink"); exists {
			return buildEntityIDFromLink(clusterSelfLink.(string))
		}
	case APIKEY_RESOURCE_TYPE:
		if apiKeyID, exists := getValue(flattenedData, assetsDataMap, "asset_vendor_id"); exists {
			return strings.ToLower(apiKeyID.(string))
		}
	case CLOUDACCOUNT_RESOURCE_TYPE:
		if assetName, exists := getValue(flattenedData, assetsDataMap, "asset_name"); exists {
			if accID, ok := parentRscNameToID[assetName.(string)]; ok {
				return accID
			}
		}
	case GCP_BIGQUERYDATASET_RESOURCE_TYPE:
		if clusterSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpBigqueryDataset.selfLink"); exists {
			return buildEntityIDFromLink(clusterSelfLink.(string))
		}
	case GCP_BIGTABLECLUSTER_RESOURCE_TYPE:
		if clusterID, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpBigtableCluster.ClusterId"); exists {
			return clusterID.(string)
		}
	case GCP_BIGTABLEINSTANCE_RESOURCE_TYPE:
		if instanceID, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpBigtableInstance.InstanceId"); exists {
			return instanceID.(string)
		}
	case GCP_SECRETMANAGERSECRET_RESOURCE_TYPE:
		if secretID, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpSecretManagerSecret.SecretId"); exists {
			return secretID.(string)
		}
	case GCP_SPANNERINSTANCE_RESOURCE_TYPE:
		if instanceID, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpSpannerInstance.Name"); exists {
			return instanceID.(string)
		}
	case GCP_STORAGEBUCKET_RESOURCE_TYPE:
		if bucketName, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpStorageBucket.Name"); exists {
			return bucketName.(string)
		}
	case GCP_VPNGATEWAY_RESOURCE_TYPE:
		if vpnSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpVpnGateway.SelfLink"); exists {
			return buildEntityIDFromLink(vpnSelfLink.(string))
		}
	case IP_RESOURCE_TYPE:
		if ip, exists := getValue(flattenedData, assetsDataMap, "model.data.Address.Name"); exists {
			return ip.(string)
		}
	case GCP_SCHEDULESNAPSHOT_RESOURCE_TYPE:
		if scheduleSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpScheduleSnapshot.selfLink"); exists {
			return buildEntityIDFromLink(scheduleSelfLink.(string))
		}
	case GCP_CLOUDRUNSERVICE_RESOURCE_TYPE:
		return "projects/" + precizeAsset.AccountName + "/locations/" + precizeAsset.Region + "/services/" + precizeAsset.ResourceName

	case GCP_SQLINSTANCE_RESOURCE_TYPE:
		if sqlSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpSqlInstance.selfLink"); exists {
			return buildEntityIDFromLink(sqlSelfLink.(string))
		}
	case GCP_VPNTUNNEL_RESOURCE_TYPE:
		if vpnTunnelSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpVpnTunnel.selfLink"); exists {
			return buildEntityIDFromLink(vpnTunnelSelfLink.(string))
		}
	case GCP_SECRETMANAGERSECRETVERSION_RESOURCE_TYPE:
		if name, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpSecretManagerSecretVersion.selfLink"); exists {
			return name.(string)
		}
	case GCP_COMPUTETARGETPOOL_RESOURCE_TYPE:
		if targetPoolSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpComputeTargetPool.selfLink"); exists {
			return buildEntityIDFromLink(targetPoolSelfLink.(string))
		}
	case GCP_FILESTOREINSTANCE_RESOURCE_TYPE:
		if name, exists := getValue(flattenedData, assetsDataMap, "model.data.name"); exists {
			return name.(string)
		}
	case GCP_LOADBALANCERBACKENDSERVICE_RESOURCE_TYPE:
		if lbSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpLoadBalancerBackendService.selfLink"); exists {
			return buildEntityIDFromLink(lbSelfLink.(string))
		}
	case FUNCTION_RESOURCE_TYPE:
		if funcId, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpCloudFunction.FunctionId"); exists {
			return funcId.(string)
		}
	case GCP_CERTIFICATE_RESOURCE_TYPE:
		if selflink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpCertificate.Selflink"); exists {
			return buildEntityIDFromLink(selflink.(string))
		}
	case GCP_DNSMANAGEDZONE_RESOURCE_TYPE:
		if zoneId, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpDnsManagedZone.DnsManagedZoneId"); exists {
			return "projects/" + precizeAsset.AccountName + "/managedzones/" + zoneId.(string)
		}
	case GCP_VPCFIREWALLRULE_RESOURCE_TYPE:
		if firewallSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpVpcFirewallRule.SelfLink"); exists {
			return buildEntityIDFromLink(firewallSelfLink.(string))
		}
	case GCP_IMAGE_RESOURCE_TYPE:
		if imageSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpVmImage.SelfLink"); exists {
			return buildEntityIDFromLink(imageSelfLink.(string))
		}
	case GCP_REDISINSTANCE_RESOURCE_TYPE:
		if redisSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpRedisInstance.InstanceId"); exists {
			return redisSelfLink.(string)
		}
	case GCP_KMSKEY_RESOURCE_TYPE:
		if entId, exists := getValue(flattenedData, assetsDataMap, "asset_name"); exists {
			return entId.(string)
		}
	case GCP_LOADBALANCER_RESOURCE_TYPE:
		if lbSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpLoadBalancer.SelfLink"); exists {
			return buildEntityIDFromLink(lbSelfLink.(string))
		}
	case GCP_MEMCACHECLUSTER_RESOURCE_TYPE:
		if entId, exists := getValue(flattenedData, assetsDataMap, "asset_name"); exists {
			return entId.(string)
		}
	case GCP_CONTAINERIMAGE_RESOURCE_TYPE:
		if imageUri, ok := getValue(flattenedData, assetsDataMap, "model.data.ContainerImage.RepositoryUri"); ok {
			imageSlipt := strings.Split(imageUri.(string), "/")
			imageName := imageSlipt[len(imageSlipt)-1]

			if repoName, ok := getValue(flattenedData, assetsDataMap, "model.data.GcpGcrImage.Repository.model.name"); ok {
				if imageDigest, ok := getValue(flattenedData, assetsDataMap, "model.data.ContainerImage.ImageDigest"); ok {
					return "projects/" + precizeAsset.AccountName + "/locations/" + precizeAsset.Region + "/repositories/" + repoName.(string) + "/dockerimages/" + imageName + "@" + imageDigest.(string)
				}
			}
		}
	case GCP_LOADBALANCERFORWARDINGRULE_RESOURCE_TYPE:
		if forwardingRuleSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpLoadBalancerForwardingRule.SelfLink"); exists {
			return buildEntityIDFromLink(forwardingRuleSelfLink.(string))
		}
	case GKE_RESOURCE_TYPE:
		if gkeSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpVmInstance.SelfLink"); exists {
			return buildEntityIDFromLink(gkeSelfLink.(string))
		}
	case GCP_BIGQUERYTABLE_RESOURCE_TYPE:
		if tableSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpBigqueryTable.SelfLink"); exists {
			return buildEntityIDFromLink(tableSelfLink.(string))
		}
	case GCP_GKECLUSTER_RESOURCE_TYPE:
		if clusterSelfLink, exists := getValue(flattenedData, assetsDataMap, "model.data.GcpGkeCluster.SelfLink"); exists {
			return buildEntityIDFromLink(clusterSelfLink.(string))
		}
	case GCP_SPANNERDATABASE_RESOURCE_TYPE:
		if databaseName, exists := getValue(flattenedData, assetsDataMap, "asset_name"); exists {
			return databaseName.(string)
		}
	case VMCONTAINER_RESOURCE_TYPE:
		if containerName, exists := getValue(flattenedData, assetsDataMap, "model.data.Container.Name"); exists {
			return "projects/" + precizeAsset.AccountName + "/locations/" + precizeAsset.Region + "/vmcontainer/" + containerName.(string)
		}
	default:
		unsupportedData.EntityIDNormalize[precizeAsset.EntityType] = struct{}{}
	}

	return
}

func buildEntityJson(flattenedData map[string]any, assetsDataMap map[string]any, precizeAsset *commonExtCr.ExternalCloudResource, unsupportedData *UnsupportedData) {
	entityJson := make(map[string]any)
	serviceType := ""
	if sType, exists := getValue(flattenedData, assetsDataMap, "asset_type"); !exists {
		return
	} else {
		serviceType = sType.(string)
	}

	propertyMappings, ok := serviceToPropertyMap[serviceType]
	if !ok {
		unsupportedData.EntityJson[serviceType] = struct{}{}
		return
	}

	mappings, ok := propertyMappings.([]map[string]any)
	if !ok {
		log.Print(logger.ERROR, "Invalid property mapping format for service type: %s", serviceType)
		return
	}

	for _, mapping := range mappings {
		key, keyOk := mapping["key"].(string)
		path, pathOk := mapping["path"].(string)

		if !keyOk || !pathOk {
			log.Print(logger.ERROR, "Invalid mapping entry for service", serviceType, mapping)
			continue
		}

		if value, found := getValue(flattenedData, assetsDataMap, path); found {
			entityJson[key] = value
		}
	}

	entityJsonBytes, err := json.Marshal(entityJson)
	if err != nil {
		logger.Print(logger.ERROR, "Error Marshalling asset json", err)
		return
	}

	precizeAsset.EntityJson = string(entityJsonBytes)
}

func buildEntityIDFromLink(link string) string {
	selfLinkSplit := strings.Split(link, "projects/")
	if len(selfLinkSplit) > 1 {
		return "projects/" + strings.ToLower(selfLinkSplit[1])
	}
	return ""
}
