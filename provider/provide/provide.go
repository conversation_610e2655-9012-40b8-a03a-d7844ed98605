package provide

import (
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/email"
	"github.com/precize/logger"
	"github.com/precize/provider/aws"
	"github.com/precize/provider/azure"
	"github.com/precize/provider/bitbucket"
	"github.com/precize/provider/gcp"
	"github.com/precize/provider/github"
	"github.com/precize/provider/gitlab"
	"github.com/precize/provider/jira"
	"github.com/precize/provider/okta"
	"github.com/precize/provider/openai"
	"github.com/precize/provider/orca"
	"github.com/precize/provider/tenant"
	"github.com/precize/provider/wiz"
)

const (
	MAX_TENANT_THREADS = 10
)

var (
	tenantQueue                            = make(chan string, 100)
	processingTenants, tenantProcessedTime sync.Map
)

func StartProvider() {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("provider")
		}
	}()

	tenant.InitTenantsProviders()
	common.InitGenericConstants()

	fetchGitPreCommits()

	for i := 0; i < MAX_TENANT_THREADS; i++ {

		go func() {
			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occured", r)
					email.SendPanicEmail("provider")
				}
			}()

			tenantWorker() // Starts MAX_TENANT_THREADS number of workers in parallel
		}()
	}

	var queuedTenants = make(map[string]struct{}) // To ensure all tenants get a chance

	for {

		tenantIDs, err := tenant.GetAllTenantIDs()
		if err != nil {
			continue
		}

		for _, tenantID := range tenantIDs {

			if len(queuedTenants) >= len(tenantIDs) {
				queuedTenants = make(map[string]struct{})
			}

			if _, ok := queuedTenants[tenantID]; ok {
				continue
			}

			select {
			case tenantQueue <- tenantID:
				queuedTenants[tenantID] = struct{}{}
			}
		}
	}
}

func tenantWorker() {
	for tenantID := range tenantQueue {
		if _, ok := processingTenants.Load(tenantID); ok {
			time.Sleep(5 * time.Second)
			continue
		}

		if v, ok := tenantProcessedTime.Load(tenantID); ok {
			if lastProcessedTime, valid := v.(time.Time); valid {
				if time.Now().UTC().Sub(lastProcessedTime) < (5 * time.Minute) {
					time.Sleep(5 * time.Second)
					// Process a tenant only after at least 5 minutes since last processing
					continue
				}
			}
		}

		processTenant(tenantID)
	}
}

func processTenant(tenantID string) {

	processingTenants.Store(tenantID, struct{}{})

	defer func() {
		processingTenants.Delete(tenantID)
		tenantProcessedTime.Store(tenantID, time.Now().UTC())
	}()

	logger.Print(logger.INFO, "Processing tenant", []string{tenantID})

	tenantData, err := tenant.GetTenantData(tenantID, true)
	if err != nil {
		return
	}

	enabledServices := tenant.GetEnabledServices(tenantData)
	gitEnabled := enabledServices[common.GITHUB_SERVICE_CODE] || enabledServices[common.GITLAB_SERVICE_CODE] || enabledServices[common.BITBUCKET_SERVICE_CODE]

	currentTime := time.Now().UTC()
	// Certain tools might not return response immediately
	delayedTime5Mins := currentTime.Add(-5 * time.Minute)
	delayedTime30Mins := currentTime.Add(-30 * time.Minute)
	delayedTime12Hrs := currentTime.Add(-12 * time.Hour)
	delayedTime24Hrs := currentTime.Add(-24 * time.Hour)

	for _, githubEnv := range tenantData.GithubAccounts.Environment {
		github.ProcessGithubData(
			tenantID,
			githubEnv,
			tenantData.FetchProgress[tenant.GITHUB_COMMIT],
			delayedTime5Mins,
			currentTime,
			false,
		)

		github.ProcessGithubWorkflows(
			tenantID,
			githubEnv,
			tenantData.FetchProgress[tenant.GITHUB_WORKFLOW],
			currentTime,
			currentTime,
		)
	}

	for _, gitlabEnv := range tenantData.GitlabAccounts.Environment {
		gitlab.ProcessGitlabData(
			tenantID,
			gitlabEnv,
			tenantData.FetchProgress[tenant.GITLAB_COMMIT],
			delayedTime5Mins,
			currentTime,
			false,
		)

		gitlab.ProcessGitlabPipelines(
			tenantID,
			gitlabEnv,
			tenantData.FetchProgress[tenant.GITLAB_PIPELINE],
			currentTime,
			currentTime,
		)
	}

	for _, bitbucketEnv := range tenantData.BitbucketAccounts.Environment {
		bitbucket.ProcessBitbucketData(
			tenantID,
			bitbucketEnv,
			tenantData.FetchProgress[tenant.BITBUCKET_COMMIT],
			delayedTime5Mins,
			currentTime,
			false,
		)
	}

	for _, jiraEnv := range tenantData.JiraAccounts.Environment {

		jira.ProcessJiraData(
			tenantID,
			jiraEnv,
			tenantData.FetchProgress[tenant.JIRA_DATA],
			currentTime,
		)

		jira.ProcessJiraIssues(
			tenantID,
			jiraEnv,
			tenantData.FetchProgress[tenant.JIRA_ISSUE],
			delayedTime12Hrs,
			currentTime,
		)
	}

	for _, oktaEnv := range tenantData.OktaAccounts.Environment {
		okta.ProcessOktaEvents(
			tenantID,
			oktaEnv,
			tenantData.FetchProgress[tenant.OKTA_EVENT],
			delayedTime30Mins,
			currentTime,
		)

		okta.ProcessOktaData(
			tenantID,
			oktaEnv,
			tenantData.FetchProgress[tenant.OKTA_APP],
			tenantData.FetchProgress[tenant.OKTA_GROUP],
			tenantData.FetchProgress[tenant.OKTA_USER],
			delayedTime5Mins,
		)
	}

	for _, orcaEnv := range tenantData.OrcaAccounts.Environment {
		if len(tenantData.AWSAccounts.Environment) > 0 {
			orca.ProcessOrcaAlerts(
				tenantID,
				common.AWS_SERVICE_CODE,
				orcaEnv,
				tenantData.FetchProgress[tenant.ORCA_ALERT],
				currentTime,
				currentTime,
			)

			orca.ProcessOrcaAssets(
				tenantID,
				common.AWS_SERVICE_CODE,
				orcaEnv,
				tenantData.FetchProgress[tenant.ORCA_ASSET],
				currentTime,
				currentTime,
			)
		}

		if len(tenantData.AzureAccounts.Environment) > 0 {
			orca.ProcessOrcaAlerts(
				tenantID,
				common.AZURE_SERVICE_CODE,
				orcaEnv,
				tenantData.FetchProgress[tenant.ORCA_ALERT],
				currentTime,
				currentTime,
			)

			orca.ProcessOrcaAssets(
				tenantID,
				common.AZURE_SERVICE_CODE,
				orcaEnv,
				tenantData.FetchProgress[tenant.ORCA_ASSET],
				currentTime,
				currentTime,
			)
		}

		if len(tenantData.GCPAccounts.Environment) > 0 {
			orca.ProcessOrcaAlerts(
				tenantID,
				common.GCP_SERVICE_CODE,
				orcaEnv,
				tenantData.FetchProgress[tenant.ORCA_ALERT],
				currentTime,
				currentTime,
			)

			orca.ProcessOrcaAssets(
				tenantID,
				common.GCP_SERVICE_CODE,
				orcaEnv,
				tenantData.FetchProgress[tenant.ORCA_ASSET],
				currentTime,
				currentTime,
			)
		}
	}

	for _, wizEnv := range tenantData.WizEnvironments.Environment {

		wiz.ProcessWizData(
			tenantID,
			wizEnv,
			tenantData.FetchProgress,
			currentTime,
			currentTime,
			tenantData,
		)

	}

	for _, awsEnv := range tenantData.AWSAccounts.Environment {

		for _, awsAccount := range awsEnv.Accounts {

			aws.ProcessSecurityHubEvents(
				tenantID,
				awsEnv.ID,
				awsAccount,
				tenantData.FetchProgress[tenant.SECHUB_FINDINGS],
				delayedTime24Hrs,
				currentTime,
			)

			if gitEnabled {

				aws.ProcessStackTemplates(
					tenantID,
					awsEnv.ExternalID,
					awsEnv.AccessKey,
					awsEnv.SecretToken,
					awsAccount,
					tenantData.FetchProgress[tenant.STACK_TEMPLATE_EVENT],
					currentTime,
					currentTime,
				)

				aws.ProcessAWSTerraformEvents(
					tenantID,
					awsEnv.ExternalID,
					awsEnv.AccessKey,
					awsEnv.SecretToken,
					awsAccount,
					tenantData.FetchProgress[tenant.AWS_TF_RESOURCE],
					currentTime,
					currentTime,
				)
			}
		}
	}

	for _, azureEnv := range tenantData.AzureAccounts.Environment {

		azure.ProcessDefenderRecommendations(
			tenantID,
			azureEnv.ID,
			tenantData.FetchProgress[tenant.DEFENDER_REC],
			delayedTime24Hrs,
			currentTime,
		)

		if gitEnabled {

			for _, subscriptionID := range azureEnv.Subscriptions {
				azure.ProcessARMTemplates(
					tenantID,
					azureEnv.ID,
					subscriptionID,
					tenantData.FetchProgress[tenant.ARM_TEMPLATE],
					currentTime,
					currentTime,
				)

				azure.ProcessAzureTerraformEvents(
					tenantID,
					azureEnv.ID,
					subscriptionID,
					tenantData.FetchProgress[tenant.AZURE_TF_RESOURCE],
					delayedTime30Mins,
					currentTime,
				)
			}
		}
	}

	for _, gcpEnv := range tenantData.GCPAccounts.Environment {

		orgID, isOrgOnboarded := common.OrgOrAccountOnboarded(tenantID, common.GCP_SERVICE_ID)

		if isOrgOnboarded {
			gcp.ProcessSecCommandCenterFindings(
				tenantID,
				gcpEnv.ID,
				orgID,
				isOrgOnboarded,
				tenantData.FetchProgress[tenant.SECURITY_COMMAND_CENTER_FINDINGS],
				currentTime,
				currentTime,
			)
		}

		for _, projectID := range gcpEnv.Projects {

			if gitEnabled {
				gcp.ProcessGCPTerraformEvents(
					tenantID,
					gcpEnv.ID,
					projectID,
					tenantData.FetchProgress[tenant.GCP_TF_RESOURCE],
					currentTime,
					currentTime,
				)
			}

			if !isOrgOnboarded {
				gcp.ProcessSecCommandCenterFindings(
					tenantID,
					gcpEnv.ID,
					projectID,
					isOrgOnboarded,
					tenantData.FetchProgress[tenant.SECURITY_COMMAND_CENTER_FINDINGS],
					currentTime,
					currentTime,
				)
			}
		}
	}

	for _, openAIEnv := range tenantData.OpenAIAccounts.Environment {
		openai.ProcessOpenAIData(
			tenantID,
			openAIEnv,
			tenantData.FetchProgress[tenant.OPENAI_RESOURCES],
			currentTime,
		)
	}

	logger.Print(logger.INFO, "Completed processing tenant", []string{tenantID})
}
