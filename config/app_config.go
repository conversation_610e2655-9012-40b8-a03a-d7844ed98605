package config

import (
	"os"

	"gopkg.in/yaml.v2"
)

const (
	QA_ENV      = "QA"
	PROD_ENV    = "prod"
	PREPROD_ENV = "preprod"
)

var (
	Environment string
)

type ApplicationConfig struct {
	Environment string `yaml:"env"`
	ScanServer  struct {
		Name    string `yaml:"name"`
		Host    string `yaml:"host"`
		Scheme  string `yaml:"scheme"`
		Port    int    `yaml:"port"`
		Servlet struct {
			ContextPath string `yaml:"context-path"`
		} `yaml:"servlet"`
	} `yaml:"scan-server"`
	Server struct {
		Name string `yaml:"name"`
		Port int    `yaml:"port"`
	} `yaml:"server"`
	Spring struct {
		ElasticSearch struct {
			Host       string `yaml:"host"`
			Port       string `yaml:"port"`
			Scheme     string `yaml:"scheme"`
			Username   string `yaml:"username"`
			Password   string `yaml:"password"`
			CACertPath string `yaml:"caCertPath"`
		} `yaml:"elasticsearch"`
	} `yaml:"spring"`
	OpenAI struct {
		APIKey string `yaml:"providerApiKey"`
	} `yaml:"openai"`
}

var AppConfig ApplicationConfig

func InitializeApplicationConfig(appConfigPath string) (defaultConf bool, err error) {
	conf, err := os.ReadFile(appConfigPath)
	if err == nil {
		if err = yaml.Unmarshal(conf, &AppConfig); err != nil {
			return
		}
	} else {

		err = nil
		defaultConf = true
		AppConfig.Environment = QA_ENV
		AppConfig.Spring.ElasticSearch.Host = "localhost"
		AppConfig.Spring.ElasticSearch.Port = "9998"
		AppConfig.Spring.ElasticSearch.Scheme = "http"
		AppConfig.OpenAI.APIKey = "s3JFP3NN5sYSrSQjxX9eNmTmuIOT14XWjRAWPfl3yqVab7QiFPyIRGInYK3xHgeNn3tjXtEBw0IovU10pzucZbNpwNepqUn1VFg+1eFDT0zFsG7AifBov6+J1HJJXOZ4ydJdXzU+rPctxe/8rsOuLVABL0JwOkBUch2OUpk8aDId1nQro4wNCW4oFg3MTlaCcHuZseIqdeAH6dhBak8a9spiO8IAkZlC18dDO2gwHAYuW0SN1aS1GxWZZ6/U4JEptemAnyMWBNgJGDp0X+AwZFIcMY80mW96zt3JnZVsxGp6uXurMSTFkKJ4zWxlP05ULa69ffJDPp6E1MroKdXCtx8VMEE="
	}

	Environment = AppConfig.Environment
	return
}
