package common

type ExternalCloudResource struct {
	ResourceGroup string `json:"resourceGroup,omitempty"`
	ID            string `json:"id"`
	EntityType    string `json:"entityType"`
	AccountID     string `json:"accountId"`
	AccountName   string `json:"accountName"`
	Region        string `json:"region"`
	ServiceID     int    `json:"serviceId"`
	EntityID      string `json:"entityId"`
	ResourceName  string `json:"-"`
	CreatedDate   string `json:"createdDate"`
	UpdatedDate   string `json:"updatedDate,omitempty"`
	Deleted       bool   `json:"deleted"`
	TenantID      string `json:"tenantId"`
	EntityJson    string `json:"entityJson"`
	InsertTime    string `json:"insertTime"`
	Source        string `json:"source"`
	Tags          []Tag  `json:"tags,omitempty"`
	TagsCount     int    `json:"tagsCount,omitempty"`
}

type Tag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
